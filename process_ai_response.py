import pandas as pd
import json
import re
from datetime import datetime

def process_ai_debug_file():
    """
    Process the AI response debug file to extract categorizations
    """
    print("🔧 PROCESSING AI RESPONSE FROM DEBUG FILE")
    print("=" * 50)
    
    try:
        # Read the debug file
        with open("ai_response_debug_20250610_164353.txt", 'r') as f:
            content = f.read()
        
        print(f"📂 Loaded debug file: {len(content)} characters")
        
        # Extract JSON part
        if '[' in content:
            start_idx = content.find('[')
            json_part = content[start_idx:]
            
            # Find the end of JSON (look for the last complete entry)
            # Since the file might be truncated, we'll extract what we can
            
            # Split into lines and process each JSON object
            lines = json_part.split('\n')
            categorizations = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('{"topic":') and line.endswith('},'):
                    # Remove trailing comma
                    line = line[:-1]
                    try:
                        entry = json.loads(line)
                        categorizations.append(entry)
                    except:
                        continue
                elif line.startswith('{"topic":') and line.endswith('}'):
                    try:
                        entry = json.loads(line)
                        categorizations.append(entry)
                    except:
                        continue
            
            print(f"✅ Extracted {len(categorizations)} categorizations from AI response")
            return categorizations
            
    except Exception as e:
        print(f"❌ Error processing debug file: {e}")
        return []

def merge_with_original_data(categorizations):
    """
    Merge AI categorizations with original CSV data
    """
    print(f"\n🔄 MERGING WITH ORIGINAL DATA")
    print("=" * 40)
    
    # Load original data
    df = pd.read_csv("trending_GB_7d_20250606-1129.csv")
    
    # Create lookup
    ai_lookup = {cat['topic'].lower(): cat['category'] for cat in categorizations}
    
    # Process original data
    categorized_topics = []
    matched_count = 0
    
    for idx, row in df.iterrows():
        trend = str(row['Trends']).strip()
        volume = str(row['Search volume']).strip()
        
        if trend and trend != 'nan' and len(trend) > 1:
            trend_lower = trend.lower()
            
            topic_data = {
                'trending_topic': trend,
                'search_volume': volume,
                'original_index': idx
            }
            
            if trend_lower in ai_lookup:
                topic_data['category'] = ai_lookup[trend_lower]
                topic_data['categorization_method'] = 'AI'
                matched_count += 1
            else:
                topic_data['category'] = 'Other'
                topic_data['categorization_method'] = 'Default'
            
            categorized_topics.append(topic_data)
    
    print(f"✅ Matched {matched_count}/{len(categorized_topics)} topics with AI results")
    return categorized_topics

def analyze_results(categorized_topics):
    """
    Analyze the categorization results
    """
    print(f"\n📊 AI CATEGORIZATION ANALYSIS")
    print("=" * 60)
    
    # Count by category
    category_counts = {}
    category_topics = {}
    
    for topic in categorized_topics:
        category = topic['category']
        category_counts[category] = category_counts.get(category, 0) + 1
        
        if category not in category_topics:
            category_topics[category] = []
        category_topics[category].append(topic)
    
    # Display summary
    print(f"{'Category':<25} | {'Count':<6} | {'Percentage':<10}")
    print("-" * 50)
    
    total_topics = len(categorized_topics)
    for category in sorted(category_counts.keys(), key=lambda x: category_counts[x], reverse=True):
        count = category_counts[category]
        percentage = (count / total_topics) * 100
        print(f"{category:<25} | {count:<6} | {percentage:<9.1f}%")
    
    print("-" * 50)
    print(f"{'TOTAL':<25} | {total_topics:<6} | {'100.0%':<10}")
    
    # Show Technology topics
    if 'Technology' in category_topics:
        tech_topics = category_topics['Technology']
        print(f"\n🎯 TECHNOLOGY TOPICS FOUND BY AI ({len(tech_topics)} topics):")
        print("=" * 70)
        
        for i, topic in enumerate(tech_topics, 1):
            print(f"{i:2d}. {topic['trending_topic']:<50} | {topic['search_volume']}")
    
    return category_counts, category_topics

def save_results(categorized_topics, category_topics):
    """
    Save results to CSV files
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"\n💾 SAVING AI RESULTS")
    print("=" * 40)
    
    files_created = []
    
    # Save complete results
    complete_df = pd.DataFrame(categorized_topics)
    complete_file = f"ai_final_all_{timestamp}.csv"
    complete_df.to_csv(complete_file, index=False)
    files_created.append(complete_file)
    print(f"📁 Complete results: {complete_file}")
    
    # Save individual category files
    for category, topics in category_topics.items():
        if len(topics) > 0:
            category_df = pd.DataFrame(topics)
            category_file = f"ai_final_{category.lower().replace(' ', '_')}_{timestamp}.csv"
            category_df.to_csv(category_file, index=False)
            files_created.append(category_file)
            print(f"📁 {category}: {category_file} ({len(topics)} topics)")
    
    return files_created

def main():
    """
    Main function to process AI categorization results
    """
    print("🤖 AI CATEGORIZATION RESULTS PROCESSOR")
    print("🧠 Processing DeepSeek AI Response")
    print("📊 Extracting Categories from Debug File")
    print("=" * 60)
    
    # Process AI response
    categorizations = process_ai_debug_file()
    if not categorizations:
        print("❌ No categorizations found")
        return
    
    # Merge with original data
    categorized_topics = merge_with_original_data(categorizations)
    
    # Analyze results
    category_counts, category_topics = analyze_results(categorized_topics)
    
    # Save results
    files_created = save_results(categorized_topics, category_topics)
    
    # Final summary
    tech_count = category_counts.get('Technology', 0)
    print(f"\n🎉 AI CATEGORIZATION PROCESSING COMPLETE!")
    print(f"📊 Total topics processed: {len(categorized_topics)}")
    print(f"🎯 Technology topics found: {tech_count}")
    print(f"📁 Files created: {len(files_created)}")
    print(f"🤖 Method: DeepSeek AI (Single Call)")
    
    # Show some sample technology topics
    if tech_count > 0:
        print(f"\n🎯 SAMPLE TECHNOLOGY TOPICS:")
        tech_topics = category_topics['Technology']
        for i, topic in enumerate(tech_topics[:10], 1):
            print(f"   {i:2d}. {topic['trending_topic']}")
    
    return categorized_topics

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n✅ SUCCESS! Processed AI categorization for {len(results)} topics!")
        else:
            print(f"\n❌ FAILED! Could not process AI categorization.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
