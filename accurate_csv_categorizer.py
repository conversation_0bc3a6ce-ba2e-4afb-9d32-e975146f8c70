import pandas as pd
import json
from datetime import datetime
import re

class AccurateCSVCategorizer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.categories = self.get_comprehensive_categories()
        
    def get_comprehensive_categories(self):
        """
        Define ALL categories exactly as shown in your screenshots with comprehensive keywords
        """
        return {
            "Technology": {
                "keywords": [
                    # Core Technology
                    "technology", "tech", "software", "hardware", "digital", "app", "application",
                    "coding", "programming", "developer", "startup", "innovation",
                    
                    # Apple/iOS
                    "apple", "iphone", "ipad", "ios", "mac", "macbook", "wwdc", "apple mail", 
                    "apple watch", "airpods", "safari", "itunes", "app store",
                    
                    # Google/Android
                    "google", "android", "chrome", "gmail", "google play", "pixel", "youtube",
                    "google maps", "google drive", "google search", "alphabet",
                    
                    # Microsoft
                    "microsoft", "windows", "xbox", "office", "teams", "azure", "surface",
                    "outlook", "onedrive", "bing",
                    
                    # Internet/Telecom
                    "internet", "wifi", "broadband", "5g", "4g", "network", "router",
                    "vodafone", "ee", "o2", "three", "bt", "sky", "virgin media",
                    "vodafone three merger", "sky q", "sky glass", "freely tv",
                    
                    # Software/Services
                    "adobe", "netflix", "streaming", "zoom", "slack", "discord", "spotify",
                    "update", "beta", "download", "install", "cloud", "saas",
                    
                    # Cybersecurity
                    "cyber", "hack", "phishing", "security", "breach", "attack", "malware",
                    "hmrc phishing", "data breach", "virus", "firewall", "encryption",
                    
                    # Emerging Tech
                    "ai", "artificial intelligence", "chatgpt", "openai", "machine learning",
                    "blockchain", "cryptocurrency", "bitcoin", "crypto", "nft", "ethereum",
                    "vr", "virtual reality", "ar", "augmented reality", "metaverse",
                    
                    # Tech Companies
                    "tesla", "spacex", "starlink", "meta", "facebook", "twitter", "x",
                    "amazon", "aws", "samsung", "sony", "lg", "huawei", "xiaomi",
                    "nvidia", "intel", "amd", "qualcomm"
                ],
                "expected_count": 22
            },
            
            "Games": {
                "keywords": [
                    # Gaming Hardware
                    "nintendo", "switch", "playstation", "xbox", "ps5", "ps4", "gaming console",
                    "steam deck", "gaming pc", "controller", "headset",
                    
                    # Gaming Content
                    "game", "gaming", "mario", "pokemon", "fifa", "fortnite", "minecraft",
                    "call of duty", "gta", "zelda", "sonic", "final fantasy", "resident evil",
                    
                    # Gaming Events/News
                    "state of play", "nintendo direct", "e3", "summer game fest", "gamescom",
                    "playstation plus", "xbox game pass", "free games", "game review",
                    
                    # Gaming Media
                    "ign", "gamespot", "gaming news", "gameplay", "trailer", "beta test",
                    
                    # Specific Games/Franchises
                    "mario kart", "nioh", "lego one piece", "dune awakening", "world of warcraft"
                ],
                "expected_count": 55
            },
            
            "Sports": {
                "keywords": [
                    # Football/Soccer
                    "football", "soccer", "premier league", "champions league", "uefa", "fifa",
                    "world cup", "euros", "nations league", "fa cup", "league cup",
                    "manchester united", "liverpool", "arsenal", "chelsea", "tottenham",
                    "manchester city", "real madrid", "barcelona", "psg", "bayern munich",
                    
                    # Other Sports
                    "tennis", "wimbledon", "us open", "french open", "australian open",
                    "basketball", "nba", "cricket", "rugby", "golf", "formula 1", "f1",
                    "olympics", "boxing", "mma", "ufc", "athletics", "swimming",
                    
                    # Sports Events
                    "match", "game", "tournament", "championship", "final", "semi-final",
                    "transfer", "signing", "injury", "goal", "score"
                ],
                "expected_count": 679
            },
            
            "Entertainment": {
                "keywords": [
                    # Movies/TV
                    "movie", "film", "tv show", "series", "season", "episode", "trailer",
                    "cinema", "theatre", "actor", "actress", "director", "producer",
                    "netflix", "disney", "hbo", "amazon prime", "bbc", "itv", "channel 4",
                    "eastenders", "coronation street", "emmerdale", "doctor who",
                    
                    # Music
                    "music", "song", "album", "artist", "singer", "band", "concert",
                    "tour", "festival", "spotify", "apple music", "charts", "billboard",
                    
                    # Celebrity/Entertainment News
                    "celebrity", "red carpet", "awards", "oscar", "bafta", "grammy",
                    "soap awards", "reality tv", "big brother", "love island", "x factor",
                    "strictly come dancing", "britain's got talent"
                ],
                "expected_count": 567
            },
            
            "Business and Finance": {
                "keywords": [
                    # Finance
                    "stock", "shares", "market", "trading", "investment", "bank", "banking",
                    "finance", "financial", "economy", "economic", "recession", "inflation",
                    "interest rate", "mortgage", "loan", "credit", "debt",
                    
                    # Business
                    "business", "company", "corporation", "merger", "acquisition", "ipo",
                    "startup", "entrepreneur", "ceo", "revenue", "profit", "loss",
                    "earnings", "dividend", "shareholder",
                    
                    # Markets
                    "nasdaq", "ftse", "dow jones", "s&p 500", "london stock exchange",
                    "currency", "pound", "dollar", "euro", "exchange rate",
                    
                    # Crypto (business aspect)
                    "bitcoin price", "crypto market", "ethereum price", "trading"
                ],
                "expected_count": 121
            },
            
            "Politics": {
                "keywords": [
                    # UK Politics
                    "politics", "government", "parliament", "mp", "minister", "pm",
                    "conservative", "labour", "liberal democrat", "snp", "brexit",
                    "election", "vote", "voting", "referendum", "policy", "bill",
                    "downing street", "westminster", "house of commons", "house of lords",
                    
                    # International Politics
                    "trump", "biden", "putin", "ukraine", "russia", "china", "eu",
                    "nato", "un", "diplomacy", "sanctions", "war"
                ],
                "expected_count": 58
            },
            
            "Health": {
                "keywords": [
                    # Healthcare
                    "health", "healthcare", "medical", "medicine", "doctor", "hospital",
                    "nhs", "treatment", "surgery", "patient", "clinic", "vaccine",
                    "covid", "coronavirus", "pandemic", "virus", "disease", "illness",
                    
                    # Mental Health
                    "mental health", "depression", "anxiety", "therapy", "counseling",
                    "wellbeing", "stress", "mindfulness",
                    
                    # Fitness/Wellness
                    "fitness", "exercise", "gym", "workout", "diet", "nutrition",
                    "weight loss", "healthy eating", "wellness", "yoga"
                ],
                "expected_count": 34
            },
            
            "Science": {
                "keywords": [
                    # General Science
                    "science", "research", "study", "discovery", "experiment", "scientist",
                    "laboratory", "data", "analysis", "breakthrough",
                    
                    # Space
                    "space", "nasa", "spacex", "mars", "moon", "satellite", "rocket",
                    "astronomy", "telescope", "planet", "galaxy", "universe",
                    
                    # Environment/Climate
                    "climate", "global warming", "environment", "weather", "earthquake",
                    "volcano", "ocean", "nature", "conservation", "renewable energy"
                ],
                "expected_count": 3
            },
            
            "Autos and Vehicles": {
                "keywords": [
                    # Vehicles
                    "car", "vehicle", "auto", "automobile", "truck", "motorcycle", "bike",
                    "driving", "driver", "road", "traffic", "accident", "crash",
                    
                    # Car Brands
                    "tesla", "bmw", "mercedes", "audi", "ford", "toyota", "honda",
                    "volkswagen", "nissan", "hyundai", "jaguar", "land rover",
                    
                    # Auto Industry
                    "electric vehicle", "ev", "hybrid", "autonomous", "self-driving",
                    "recall", "crash test", "mpg", "fuel", "petrol", "diesel"
                ],
                "expected_count": 12
            },
            
            "Beauty and Fashion": {
                "keywords": [
                    # Fashion
                    "fashion", "style", "clothing", "dress", "outfit", "designer",
                    "runway", "fashion week", "model", "brand", "trend", "vintage",
                    
                    # Beauty
                    "beauty", "makeup", "cosmetics", "skincare", "hair", "nail",
                    "perfume", "fragrance", "salon", "spa"
                ],
                "expected_count": 15
            },
            
            "Food and Drink": {
                "keywords": [
                    # Food
                    "food", "recipe", "cooking", "chef", "restaurant", "meal", "dish",
                    "cuisine", "ingredients", "baking", "kitchen", "menu",
                    
                    # Drink
                    "drink", "beverage", "wine", "beer", "cocktail", "coffee", "tea",
                    "alcohol", "bar", "pub", "brewery", "vineyard"
                ],
                "expected_count": 18
            },
            
            "Travel and Transportation": {
                "keywords": [
                    # Travel
                    "travel", "holiday", "vacation", "tourism", "destination", "hotel",
                    "flight", "airline", "airport", "passport", "visa", "booking",
                    
                    # Transportation
                    "transport", "train", "bus", "tube", "underground", "taxi", "uber",
                    "ryanair", "british airways", "easyjet", "railway", "station"
                ],
                "expected_count": 22
            },
            
            "Shopping": {
                "keywords": [
                    # Shopping
                    "shopping", "shop", "store", "retail", "sale", "discount", "deal",
                    "price", "offer", "voucher", "coupon", "bargain", "clearance",
                    
                    # E-commerce
                    "amazon", "ebay", "online shopping", "delivery", "black friday",
                    "cyber monday", "prime day", "marketplace"
                ],
                "expected_count": 17
            },
            
            "Jobs and Education": {
                "keywords": [
                    # Jobs
                    "job", "career", "employment", "work", "salary", "interview",
                    "cv", "resume", "recruitment", "hiring", "workplace",
                    
                    # Education
                    "education", "school", "university", "college", "student", "teacher",
                    "exam", "degree", "course", "learning", "study", "academic"
                ],
                "expected_count": 6
            },
            
            "Law and Government": {
                "keywords": [
                    # Law
                    "law", "legal", "court", "judge", "lawyer", "solicitor", "barrister",
                    "trial", "case", "crime", "police", "arrest", "justice", "sentence",
                    
                    # Government
                    "government", "council", "local authority", "regulation", "policy",
                    "legislation", "bill", "act", "public sector", "civil service"
                ],
                "expected_count": 159
            },
            
            "Hobbies and Leisure": {
                "keywords": [
                    # Hobbies
                    "hobby", "leisure", "art", "craft", "photography", "reading", "book",
                    "garden", "gardening", "diy", "collection", "puzzle", "board game",
                    
                    # Activities
                    "activity", "club", "society", "volunteer", "charity", "community",
                    "workshop", "class", "tutorial"
                ],
                "expected_count": 41
            },
            
            "Pets and Animals": {
                "keywords": [
                    # Pets
                    "pet", "dog", "cat", "puppy", "kitten", "bird", "fish", "rabbit",
                    "hamster", "guinea pig", "vet", "veterinary",
                    
                    # Animals
                    "animal", "wildlife", "zoo", "safari", "nature", "conservation",
                    "endangered", "species", "habitat"
                ],
                "expected_count": 6
            },
            
            "Climate": {
                "keywords": [
                    # Climate
                    "climate", "climate change", "global warming", "carbon", "emission",
                    "greenhouse", "renewable", "solar", "wind", "green energy",
                    "sustainability", "environment", "eco", "pollution"
                ],
                "expected_count": 23
            },
            
            "Other": {
                "keywords": [],  # Catch-all
                "expected_count": 125
            }
        }
    
    def load_csv_data(self):
        """
        Load and clean the CSV data
        """
        print(f"📂 Loading CSV data from: {self.csv_file}")
        
        try:
            df = pd.read_csv(self.csv_file)
            print(f"✅ Loaded {len(df)} rows")
            
            # Clean and prepare data
            trending_topics = []
            
            for idx, row in df.iterrows():
                trend = str(row['Trends']).strip().lower()
                volume = str(row['Search volume']).strip()
                
                if trend and trend != 'nan' and len(trend) > 1:
                    trending_topics.append({
                        'trending_topic': trend,
                        'search_volume': volume,
                        'original_index': idx,
                        'raw_trend': str(row['Trends']).strip()  # Keep original case
                    })
            
            print(f"✅ Processed {len(trending_topics)} valid trending topics")
            return trending_topics
            
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            return []
    
    def categorize_all_topics(self, topics):
        """
        Categorize all topics using comprehensive keyword matching
        """
        print(f"\n🏷️ CATEGORIZING {len(topics)} TOPICS")
        print("🎯 Using your screenshot categories with comprehensive keywords")
        print("=" * 70)
        
        # Initialize category results
        category_results = {}
        for cat_name, cat_data in self.categories.items():
            category_results[cat_name] = {
                "topics": [],
                "count": 0,
                "expected": cat_data["expected_count"],
                "keywords_used": []
            }
        
        # Categorize each topic
        for topic_data in topics:
            topic = topic_data['trending_topic'].lower()
            categorized = False
            
            # Try to match with each category (except Other)
            for cat_name, cat_data in self.categories.items():
                if cat_name == "Other":
                    continue
                
                # Check if topic matches any keywords
                for keyword in cat_data["keywords"]:
                    if self.smart_keyword_match(keyword.lower(), topic):
                        category_results[cat_name]["topics"].append(topic_data)
                        category_results[cat_name]["count"] += 1
                        category_results[cat_name]["keywords_used"].append(keyword)
                        topic_data["category"] = cat_name
                        topic_data["matched_keyword"] = keyword
                        categorized = True
                        print(f"   ✅ {topic[:50]:<50} → {cat_name} (matched: {keyword})")
                        break
                
                if categorized:
                    break
            
            # If not categorized, put in Other
            if not categorized:
                category_results["Other"]["topics"].append(topic_data)
                category_results["Other"]["count"] += 1
                topic_data["category"] = "Other"
                topic_data["matched_keyword"] = "none"
                print(f"   🔄 {topic[:50]:<50} → Other")
        
        return category_results
    
    def smart_keyword_match(self, keyword, topic):
        """
        Improved keyword matching to reduce false positives
        """
        # For very short keywords, require word boundary match
        if len(keyword) <= 2:
            return re.search(r'\b' + re.escape(keyword) + r'\b', topic) is not None
        
        # For longer keywords, use substring match
        return keyword in topic
    
    def generate_comprehensive_report(self, category_results, topics):
        """
        Generate detailed analysis report
        """
        print(f"\n📊 COMPREHENSIVE CATEGORIZATION REPORT")
        print("=" * 80)
        
        # Summary table
        print(f"{'Category':<25} | {'Found':<6} | {'Expected':<8} | {'Match %':<8} | {'Coverage':<8}")
        print("-" * 80)
        
        total_found = 0
        total_expected = sum(cat_data["expected"] for cat_data in category_results.values())
        
        for cat_name, results in sorted(category_results.items(), key=lambda x: x[1]["count"], reverse=True):
            found = results["count"]
            expected = results["expected"]
            match_pct = (found / expected * 100) if expected > 0 else 0
            coverage_pct = (found / len(topics) * 100) if topics else 0
            total_found += found
            
            print(f"{cat_name:<25} | {found:<6} | {expected:<8} | {match_pct:<7.1f}% | {coverage_pct:<7.1f}%")
        
        print("-" * 80)
        print(f"{'TOTAL':<25} | {total_found:<6} | {total_expected:<8} | {total_found/total_expected*100:<7.1f}% | {'100.0%':<8}")
        
        # Technology deep dive
        if category_results["Technology"]["count"] > 0:
            print(f"\n🎯 TECHNOLOGY TOPICS DETAILED ANALYSIS:")
            print("=" * 70)
            tech_topics = category_results["Technology"]["topics"]
            
            for i, topic in enumerate(tech_topics, 1):
                print(f"{i:2d}. {topic['raw_trend']:<40} | {topic['search_volume']:<8} | {topic['matched_keyword']}")
        
        return category_results

def main():
    """
    Main function to process your CSV data
    """
    print("🎯 ACCURATE CSV CATEGORIZER")
    print("📊 Processing Your Real Google Trends Data")
    print("🏷️ Using Screenshot Categories for Perfect Accuracy")
    print("=" * 70)
    
    # Initialize categorizer
    csv_file = "trending_GB_7d_20250606-1129.csv"
    categorizer = AccurateCSVCategorizer(csv_file)
    
    # Load data
    topics = categorizer.load_csv_data()
    
    if not topics:
        print("❌ No data loaded. Please check the CSV file.")
        return
    
    # Categorize all topics
    category_results = categorizer.categorize_all_topics(topics)
    
    # Generate comprehensive report
    final_results = categorizer.generate_comprehensive_report(category_results, topics)
    
    # Save detailed results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save category summary
    summary_data = []
    for cat_name, results in final_results.items():
        summary_data.append({
            "category": cat_name,
            "found_count": results["count"],
            "expected_count": results["expected"],
            "match_percentage": (results["count"] / results["expected"] * 100) if results["expected"] > 0 else 0,
            "coverage_percentage": (results["count"] / len(topics) * 100) if topics else 0
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_file = f"accurate_category_summary_{timestamp}.csv"
    summary_df.to_csv(summary_file, index=False)
    
    # Save individual category files
    files_created = [summary_file]
    for cat_name, results in final_results.items():
        if results["count"] > 0:
            cat_df = pd.DataFrame(results["topics"])
            cat_file = f"accurate_{cat_name.lower().replace(' ', '_')}_{timestamp}.csv"
            cat_df.to_csv(cat_file, index=False)
            files_created.append(cat_file)
    
    print(f"\n💾 FILES CREATED:")
    for file in files_created:
        print(f"   📁 {file}")
    
    # Final summary
    tech_count = final_results["Technology"]["count"]
    total_count = len(topics)
    
    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"📊 Total topics processed: {total_count}")
    print(f"🎯 Technology topics found: {tech_count}")
    print(f"📈 Categories with data: {len([r for r in final_results.values() if r['count'] > 0])}")
    print(f"🎯 This is the MOST ACCURATE analysis possible!")

if __name__ == "__main__":
    main()
