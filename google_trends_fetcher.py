from pytrends.request import TrendReq
import pandas as pd
from time import sleep
import re

def fetch_google_trends_data(keywords, 
                            timeframe='now 7-d', base_sleep=60, 
                            geo='US', 
                            category=20, 
                            sort_method='top',
                            retries=3):
    """
    Fetch Google Trends data with specified filters and error handling
    
    Args:
        keywords (list): List of search terms
        timeframe (str): Date range for trends data
        base_sleep (int): Base sleep time between retries
        geo (str): Geographic location filter (ISO 3166-2 code)
        category (int): Search category ID
        sort_method (str): 'top' or 'rising'
        retries (int): Number of API retry attempts
    
    Returns:
        pandas.DataFrame: Trends data with timestamps
    """
    
    # Validate input parameters
    if not isinstance(keywords, list) or len(keywords) == 0:
        raise ValueError("Keywords must be a non-empty list")
    
    valid_sort_methods = ['top', 'rising']
    if sort_method.lower() not in valid_sort_methods:
        raise ValueError(f"Invalid sort method. Use: {valid_sort_methods}")

    # Validate timeframe format
    if not re.match(r'^(now\s+[1-9][0-9]*-[dm]|today\s+[1-9][0-9]*-d|\d{4}-\d{2}-\d{2}\s+\d{4}-\d{2}-\d{2})$', timeframe):
        raise ValueError("Invalid timeframe format. Use 'now 7-d' or 'YYYY-MM-DD YYYY-MM-DD'")

    pytrends = TrendReq()
    
    for attempt in range(retries):
        try:
            pytrends.build_payload(
                kw_list=keywords,
                timeframe=timeframe,
                geo=geo,
                cat=category,
                hl='en-US',
            )
            
            if sort_method == 'top':
                df = pytrends.interest_over_time()
            else:
                df = pytrends.interest_over_time()
                df = df[df['isPartial'] == False].sort_values(by=keywords[0], ascending=False)
                
            if not df.empty:
                return df.drop(columns=['isPartial'])
                
        except Exception as e:
            if attempt < retries - 1:
                sleep_duration = 2 ** attempt
                print(f"Retrying in {sleep_duration} seconds...")
                sleep(max(60, sleep_duration + base_sleep))  # Fixed: use base_sleep parameter
                continue
            raise RuntimeError(f"API Error: {str(e)}. Request params: {pytrends.request_args}")

    return pd.DataFrame()  # Return empty dataframe if all retries fail


if __name__ == "__main__":
    # Example usage
    try:
        trends_df = fetch_google_trends_data(
            keywords=['AI', 'Machine Learning'],
            timeframe='now 7-d', 
            base_sleep=60,  # Fixed: use base_sleep parameter name
            geo='US',
            category=20  # Technology category
        )
        print(trends_df.head())
    except Exception as e:
        print(f"Error: {e}")
