import pandas as pd
import json
import requests
from datetime import datetime
import time

class DeepSeekCategorizer:
    def __init__(self, api_key, csv_file):
        self.api_key = api_key
        self.csv_file = csv_file
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        
        # Categories from your screenshots
        self.categories = [
            "Technology", "Games", "Sports", "Entertainment", "Business and Finance",
            "Politics", "Health", "Science", "Autos and Vehicles", "Beauty and Fashion",
            "Food and Drink", "Travel and Transportation", "Shopping", "Jobs and Education",
            "Law and Government", "Hobbies and Leisure", "Pets and Animals", "Climate", "Other"
        ]
    
    def load_csv_data(self):
        """Load trending topics from CSV"""
        print(f"📂 Loading CSV data from: {self.csv_file}")
        
        try:
            df = pd.read_csv(self.csv_file)
            print(f"✅ Loaded {len(df)} rows")
            
            trending_topics = []
            for idx, row in df.iterrows():
                trend = str(row['Trends']).strip()
                volume = str(row['Search volume']).strip()
                
                if trend and trend != 'nan' and len(trend) > 1:
                    trending_topics.append({
                        'trending_topic': trend,
                        'search_volume': volume,
                        'original_index': idx
                    })
            
            print(f"✅ Processed {len(trending_topics)} valid trending topics")
            return trending_topics
            
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            return []
    
    def categorize_batch_with_ai(self, topics_batch):
        """Use DeepSeek AI to categorize a batch of topics"""
        
        # Create the prompt
        topics_text = "\n".join([f"{i+1}. {topic['trending_topic']}" for i, topic in enumerate(topics_batch)])
        
        prompt = f"""You are an expert at categorizing trending topics. Please categorize each of the following trending topics into ONE of these exact categories:

Categories: {', '.join(self.categories)}

Trending Topics to Categorize:
{topics_text}

Rules:
1. Choose the MOST appropriate category for each topic
2. Use ONLY the categories listed above
3. If unsure, use "Other"
4. Return ONLY a JSON array with this format:
[
  {{"topic": "topic name", "category": "Category Name"}},
  {{"topic": "topic name", "category": "Category Name"}}
]

Important: Return ONLY the JSON array, no other text."""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 2000
        }
        
        try:
            print(f"🤖 Asking DeepSeek AI to categorize {len(topics_batch)} topics...")
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            ai_response = result['choices'][0]['message']['content'].strip()
            
            # Parse the JSON response
            try:
                # Clean the response (remove any markdown formatting)
                if ai_response.startswith('```json'):
                    ai_response = ai_response.replace('```json', '').replace('```', '').strip()
                elif ai_response.startswith('```'):
                    ai_response = ai_response.replace('```', '').strip()
                
                categorizations = json.loads(ai_response)
                print(f"✅ AI successfully categorized {len(categorizations)} topics")
                return categorizations
                
            except json.JSONDecodeError as e:
                print(f"❌ Error parsing AI response: {e}")
                print(f"Raw response: {ai_response[:200]}...")
                return []
                
        except Exception as e:
            print(f"❌ Error calling DeepSeek API: {e}")
            return []
    
    def categorize_all_topics(self, topics):
        """Categorize all topics using AI in batches"""
        print(f"\n🤖 AI CATEGORIZATION WITH DEEPSEEK")
        print("🎯 Using AI to intelligently categorize topics")
        print("=" * 60)
        
        # Process in batches to avoid token limits
        batch_size = 20
        all_categorizations = []
        
        for i in range(0, len(topics), batch_size):
            batch = topics[i:i+batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(topics) + batch_size - 1) // batch_size
            
            print(f"\n📊 Processing batch {batch_num}/{total_batches} ({len(batch)} topics)")
            
            categorizations = self.categorize_batch_with_ai(batch)
            
            if categorizations:
                all_categorizations.extend(categorizations)
                
                # Show some results
                print("   Sample categorizations:")
                for cat in categorizations[:3]:
                    print(f"   ✅ {cat['topic'][:40]:<40} → {cat['category']}")
            
            # Rate limiting - be respectful to the API
            if i + batch_size < len(topics):
                print("   ⏳ Waiting 2 seconds...")
                time.sleep(2)
        
        print(f"\n📊 AI categorization complete: {len(all_categorizations)} topics categorized")
        return all_categorizations
    
    def merge_ai_results_with_data(self, topics, ai_categorizations):
        """Merge AI categorization results with original data"""
        print("\n🔄 Merging AI results with original data...")
        
        # Create lookup dictionary
        ai_lookup = {cat['topic'].lower(): cat['category'] for cat in ai_categorizations}
        
        # Add categories to original topics
        categorized_topics = []
        for topic in topics:
            topic_lower = topic['trending_topic'].lower()
            
            if topic_lower in ai_lookup:
                topic['category'] = ai_lookup[topic_lower]
                topic['categorization_method'] = 'AI'
            else:
                topic['category'] = 'Other'
                topic['categorization_method'] = 'Default'
            
            categorized_topics.append(topic)
        
        return categorized_topics
    
    def generate_category_summary(self, categorized_topics):
        """Generate summary by category"""
        print(f"\n📊 GENERATING CATEGORY SUMMARY")
        print("=" * 60)
        
        # Count by category
        category_counts = {}
        category_topics = {}
        
        for topic in categorized_topics:
            category = topic['category']
            category_counts[category] = category_counts.get(category, 0) + 1
            
            if category not in category_topics:
                category_topics[category] = []
            category_topics[category].append(topic)
        
        # Display summary
        print(f"{'Category':<25} | {'Count':<6} | {'Percentage':<10}")
        print("-" * 50)
        
        total_topics = len(categorized_topics)
        for category in sorted(category_counts.keys(), key=lambda x: category_counts[x], reverse=True):
            count = category_counts[category]
            percentage = (count / total_topics) * 100
            print(f"{category:<25} | {count:<6} | {percentage:<9.1f}%")
        
        print("-" * 50)
        print(f"{'TOTAL':<25} | {total_topics:<6} | {'100.0%':<10}")
        
        # Show Technology topics in detail
        if 'Technology' in category_topics:
            tech_topics = category_topics['Technology']
            print(f"\n🎯 TECHNOLOGY TOPICS ({len(tech_topics)} found):")
            print("=" * 70)
            
            for i, topic in enumerate(tech_topics, 1):
                print(f"{i:2d}. {topic['trending_topic']:<50} | {topic['search_volume']}")
        
        return category_counts, category_topics
    
    def save_results(self, categorized_topics, category_topics):
        """Save all results to CSV files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        print(f"\n💾 SAVING RESULTS")
        print("=" * 40)
        
        files_created = []
        
        # Save complete results
        complete_df = pd.DataFrame(categorized_topics)
        complete_file = f"ai_categorized_all_{timestamp}.csv"
        complete_df.to_csv(complete_file, index=False)
        files_created.append(complete_file)
        print(f"📁 Complete results: {complete_file}")
        
        # Save individual category files
        for category, topics in category_topics.items():
            if len(topics) > 0:
                category_df = pd.DataFrame(topics)
                category_file = f"ai_{category.lower().replace(' ', '_')}_{timestamp}.csv"
                category_df.to_csv(category_file, index=False)
                files_created.append(category_file)
                print(f"📁 {category}: {category_file} ({len(topics)} topics)")
        
        # Save summary
        summary_data = []
        for category, topics in category_topics.items():
            summary_data.append({
                'category': category,
                'count': len(topics),
                'percentage': (len(topics) / len(categorized_topics)) * 100
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_file = f"ai_category_summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        files_created.append(summary_file)
        print(f"📁 Summary: {summary_file}")
        
        return files_created

def main():
    """Main function for AI categorization"""
    print("🤖 AI-POWERED TRENDING TOPICS CATEGORIZER")
    print("🧠 Using DeepSeek AI for Intelligent Categorization")
    print("📊 Processing Your Real Google Trends Data")
    print("=" * 70)
    
    # Configuration
    api_key = "sk-d137c2aa1d714ef3a4839a3e5b09aded"
    csv_file = "trending_GB_7d_20250606-1129.csv"
    
    # Initialize categorizer
    categorizer = DeepSeekCategorizer(api_key, csv_file)
    
    # Load data
    topics = categorizer.load_csv_data()
    if not topics:
        print("❌ No data loaded. Exiting.")
        return
    
    # Categorize with AI
    ai_categorizations = categorizer.categorize_all_topics(topics)
    if not ai_categorizations:
        print("❌ AI categorization failed. Exiting.")
        return
    
    # Merge results
    categorized_topics = categorizer.merge_ai_results_with_data(topics, ai_categorizations)
    
    # Generate summary
    category_counts, category_topics = categorizer.generate_category_summary(categorized_topics)
    
    # Save results
    files_created = categorizer.save_results(categorized_topics, category_topics)
    
    # Final summary
    tech_count = category_counts.get('Technology', 0)
    print(f"\n🎉 AI CATEGORIZATION COMPLETE!")
    print(f"📊 Total topics processed: {len(categorized_topics)}")
    print(f"🎯 Technology topics found: {tech_count}")
    print(f"📁 Files created: {len(files_created)}")
    print(f"🤖 Method: DeepSeek AI (much smarter than keyword matching!)")
    
    return categorized_topics

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n✅ SUCCESS! AI categorized {len(results)} topics intelligently!")
        else:
            print(f"\n❌ FAILED! AI categorization did not complete.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
