import requests
import pandas as pd
import json
import time
from datetime import datetime

class SingleAPICallScraper:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://serpapi.com/search"
        
    def get_maximum_trending_topics_single_call(self):
        """
        Get maximum trending topics with ONLY ONE API call
        Using the most effective query and parameters
        """
        print("🔥 SINGLE API CALL TRENDING SCRAPER")
        print("💰 CONSERVING YOUR 100 API CALLS/MONTH QUOTA")
        print("🎯 MAXIMIZING RESULTS WITH ONE STRATEGIC CALL")
        print("=" * 60)
        
        # The most effective single query based on our testing
        # This should capture the broadest range of trending topics
        optimal_query = "trending now UK 2025"
        
        print(f"🎯 Strategic Query: '{optimal_query}'")
        print("📊 Optimizing parameters for maximum results...")
        
        params = {
            'engine': 'google',
            'q': optimal_query,
            'location': 'United Kingdom',
            'gl': 'uk',  # Country: United Kingdom
            'hl': 'en',  # Language: English
            'num': 100,  # Maximum results per page
            'start': 0,  # Start from first result
            'api_key': self.api_key,
            'tbm': '',  # Regular search (not images/news/etc)
            'safe': 'off',  # No content filtering
            'filter': '0',  # Disable similar results filtering to get more variety
            'nfpr': '0',  # Include auto-corrected results
            'output': 'json',
            'device': 'desktop'  # Desktop for maximum results
        }
        
        try:
            print("🚀 Making SINGLE API call...")
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            print("✅ API call successful!")
            print("🔍 Extracting maximum topics from response...")
            
            # Extract ALL possible topics from the response
            topics = self.extract_all_topics_comprehensive(data)
            
            print(f"📊 TOTAL TOPICS EXTRACTED: {len(topics)}")
            print(f"💰 API CALLS USED: 1/100 (99 remaining)")
            
            return topics, data
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return [], {}
    
    def extract_all_topics_comprehensive(self, data):
        """
        Extract EVERY possible trending topic from the API response
        Maximize value from single API call
        """
        print("🔍 Comprehensive topic extraction...")
        
        all_topics = []
        
        # 1. Extract from organic results
        if 'organic_results' in data:
            print(f"   📄 Processing {len(data['organic_results'])} organic results...")
            for result in data['organic_results']:
                title = result.get('title', '').strip()
                snippet = result.get('snippet', '').strip()
                
                if title and len(title) > 2:
                    all_topics.append({
                        'topic': title.lower(),
                        'source': 'organic_title',
                        'volume': 'N/A',
                        'snippet': snippet[:100]
                    })
                
                # Extract trending topics mentioned in snippets
                if snippet:
                    # Look for patterns like "trending: topic1, topic2, topic3"
                    trending_patterns = [
                        'trending:', 'popular:', 'viral:', 'hot topics:',
                        'most searched:', 'top searches:', 'breaking:'
                    ]
                    
                    for pattern in trending_patterns:
                        if pattern in snippet.lower():
                            # Extract topics after the pattern
                            parts = snippet.lower().split(pattern)
                            if len(parts) > 1:
                                topics_text = parts[1].split('.')[0]  # Take first sentence
                                # Split by common delimiters
                                potential_topics = []
                                for delimiter in [',', ';', '|', ' and ', ' & ']:
                                    if delimiter in topics_text:
                                        potential_topics.extend(topics_text.split(delimiter))
                                        break
                                
                                for topic in potential_topics:
                                    topic = topic.strip()
                                    if topic and len(topic) > 2 and len(topic) < 50:
                                        all_topics.append({
                                            'topic': topic,
                                            'source': 'snippet_extraction',
                                            'volume': 'N/A',
                                            'snippet': f'From: {pattern}'
                                        })
        
        # 2. Extract from related searches
        if 'related_searches' in data:
            print(f"   🔗 Processing {len(data['related_searches'])} related searches...")
            for related in data['related_searches']:
                query = related.get('query', '').strip()
                if query and len(query) > 2:
                    all_topics.append({
                        'topic': query.lower(),
                        'source': 'related_searches',
                        'volume': 'N/A',
                        'snippet': ''
                    })
        
        # 3. Extract from people also ask
        if 'people_also_ask' in data:
            print(f"   ❓ Processing {len(data['people_also_ask'])} people also ask...")
            for paa in data['people_also_ask']:
                question = paa.get('question', '').strip()
                if question and len(question) > 5:
                    # Clean up question format
                    clean_question = question.lower().replace('what is ', '').replace('who is ', '').replace('?', '')
                    all_topics.append({
                        'topic': clean_question,
                        'source': 'people_also_ask',
                        'volume': 'N/A',
                        'snippet': question
                    })
        
        # 4. Extract from knowledge graph
        if 'knowledge_graph' in data:
            print("   🧠 Processing knowledge graph...")
            kg = data['knowledge_graph']
            if 'title' in kg:
                all_topics.append({
                    'topic': kg['title'].lower(),
                    'source': 'knowledge_graph',
                    'volume': 'N/A',
                    'snippet': kg.get('description', '')[:100]
                })
        
        # 5. Extract from shopping results (if any)
        if 'shopping_results' in data:
            print(f"   🛒 Processing {len(data['shopping_results'])} shopping results...")
            for shop in data['shopping_results']:
                title = shop.get('title', '').strip()
                if title and len(title) > 2:
                    all_topics.append({
                        'topic': title.lower(),
                        'source': 'shopping_results',
                        'volume': 'N/A',
                        'snippet': ''
                    })
        
        # 6. Extract from news results (if any)
        if 'news_results' in data:
            print(f"   📰 Processing {len(data['news_results'])} news results...")
            for news in data['news_results']:
                title = news.get('title', '').strip()
                if title and len(title) > 2:
                    all_topics.append({
                        'topic': title.lower(),
                        'source': 'news_results',
                        'volume': 'N/A',
                        'snippet': news.get('snippet', '')[:100]
                    })
        
        # 7. Extract from video results (if any)
        if 'video_results' in data:
            print(f"   🎥 Processing {len(data['video_results'])} video results...")
            for video in data['video_results']:
                title = video.get('title', '').strip()
                if title and len(title) > 2:
                    all_topics.append({
                        'topic': title.lower(),
                        'source': 'video_results',
                        'volume': 'N/A',
                        'snippet': ''
                    })
        
        print(f"   📊 Raw topics extracted: {len(all_topics)}")
        
        # Clean and deduplicate
        cleaned_topics = self.clean_and_deduplicate_single_call(all_topics)
        
        return cleaned_topics
    
    def clean_and_deduplicate_single_call(self, topics):
        """
        Clean and deduplicate topics from single API call
        """
        print("🧹 Cleaning and deduplicating...")
        
        seen_topics = set()
        cleaned_topics = []
        
        for topic_data in topics:
            topic = topic_data['topic'].strip().lower()
            
            # Skip if empty or too short
            if not topic or len(topic) < 3:
                continue
            
            # Skip if already seen
            if topic in seen_topics:
                continue
            
            # Skip technical/irrelevant content
            skip_patterns = [
                'http', 'www.', '.com', '.co.uk', 'javascript', 'cookie',
                'privacy policy', 'terms of service', 'contact us',
                'subscribe', 'newsletter', 'advertisement', 'sponsored'
            ]
            
            if any(pattern in topic for pattern in skip_patterns):
                continue
            
            # Skip if too long (likely not a trending topic)
            if len(topic) > 100:
                continue
            
            # Add to cleaned results
            seen_topics.add(topic)
            cleaned_topics.append({
                'trending_topic': topic,
                'search_volume': topic_data['volume'],
                'trend_direction': 'N/A',
                'time_ago': 'N/A',
                'status': 'Active',
                'source': f"SerpAPI_Single_{topic_data['source']}",
                'snippet': topic_data['snippet']
            })
        
        print(f"   ✅ Clean unique topics: {len(cleaned_topics)}")
        return cleaned_topics
    
    def analyze_single_call_results(self, topics):
        """
        Analyze results from single API call
        """
        print(f"\n📊 SINGLE API CALL ANALYSIS")
        print("=" * 50)
        
        # Count by source
        source_counts = {}
        for topic in topics:
            source = topic['source']
            source_counts[source] = source_counts.get(source, 0) + 1
        
        print("📈 Topics by Source:")
        for source, count in sorted(source_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {source}: {count} topics")
        
        # Technology analysis
        tech_keywords = [
            'nintendo', 'mario', 'playstation', 'xbox', 'gaming', 'game',
            'google', 'apple', 'microsoft', 'tesla', 'tech', 'ai',
            'iphone', 'android', 'app', 'software', 'crypto', 'bitcoin',
            'netflix', 'streaming', 'youtube', 'tiktok'
        ]
        
        tech_topics = []
        for topic in topics:
            topic_text = topic['trending_topic'].lower()
            if any(keyword in topic_text for keyword in tech_keywords):
                tech_topics.append(topic)
        
        print(f"\n🎮 TECHNOLOGY TOPICS: {len(tech_topics)}")
        
        # Show samples
        print(f"\n🎯 SAMPLE TOPICS (first 15):")
        for i, topic in enumerate(topics[:15], 1):
            print(f"{i:2d}. {topic['trending_topic'][:50]:<50} | {topic['source']}")
        
        return source_counts, tech_topics

def main():
    """
    Main function - SINGLE API CALL ONLY
    """
    print("💰 SINGLE API CALL TRENDING SCRAPER")
    print("🎯 MAXIMUM VALUE FROM ONE API CALL")
    print("📊 CONSERVING YOUR 100/MONTH QUOTA")
    print("=" * 60)
    
    # Your API key
    api_key = "418f677d8495a6aac444ed1bf0c8879cb53771578c4db1be2678fef8b2eede0f"
    
    # Initialize scraper
    scraper = SingleAPICallScraper(api_key)
    
    # Get trending topics with SINGLE API call
    topics, raw_data = scraper.get_maximum_trending_topics_single_call()
    
    if topics:
        # Analyze results
        source_counts, tech_topics = scraper.analyze_single_call_results(topics)
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"single_call_trends_{timestamp}.csv"
        
        df = pd.DataFrame(topics)
        df.to_csv(filename, index=False)
        
        # Save raw API response for debugging
        raw_filename = f"single_call_raw_{timestamp}.json"
        with open(raw_filename, 'w') as f:
            json.dump(raw_data, f, indent=2)
        
        print(f"\n🎉 SINGLE API CALL COMPLETE!")
        print(f"📊 Total topics found: {len(topics)}")
        print(f"🎮 Technology topics: {len(tech_topics)}")
        print(f"💾 Results saved: {filename}")
        print(f"💰 API calls used: 1/100 (99 remaining)")
        print(f"📈 Cost efficiency: {len(topics)} topics per API call")
        
        return topics
    else:
        print("❌ No topics found")
        return []

if __name__ == "__main__":
    try:
        results = main()
        
        if results:
            print(f"\n✅ SUCCESS! {len(results)} topics from SINGLE API call")
            print("💰 Your API quota is preserved!")
        else:
            print("\n❌ FAILED! No results from single API call")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
