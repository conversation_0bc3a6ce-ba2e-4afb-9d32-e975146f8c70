import pandas as pd
import json
import requests
from datetime import datetime
import time

class MultiBatchAICategorizer:
    def __init__(self, api_key, csv_file):
        self.api_key = api_key
        self.csv_file = csv_file
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        
        # Categories from your screenshots
        self.categories = [
            "Technology", "Games", "Sports", "Entertainment", "Business and Finance",
            "Politics", "Health", "Science", "Autos and Vehicles", "Beauty and Fashion",
            "Food and Drink", "Travel and Transportation", "Shopping", "Jobs and Education",
            "Law and Government", "Hobbies and Leisure", "Pets and Animals", "Climate", "Other"
        ]
    
    def load_csv_data(self):
        """Load all trending topics from CSV"""
        print(f"📂 Loading CSV data from: {self.csv_file}")
        
        try:
            df = pd.read_csv(self.csv_file)
            print(f"✅ Loaded {len(df)} rows")
            
            trending_topics = []
            for idx, row in df.iterrows():
                trend = str(row['Trends']).strip()
                volume = str(row['Search volume']).strip()
                
                if trend and trend != 'nan' and len(trend) > 1:
                    trending_topics.append({
                        'trending_topic': trend,
                        'search_volume': volume,
                        'original_index': idx
                    })
            
            print(f"✅ Processed {len(trending_topics)} valid trending topics")
            return trending_topics
            
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            return []
    
    def split_into_batches(self, topics, batch_size=150):
        """Split topics into smaller, manageable batches"""
        batches = []

        for i in range(0, len(topics), batch_size):
            batch = topics[i:i + batch_size]
            batches.append(batch)

        print(f"📊 Split {len(topics)} topics into {len(batches)} batches:")
        for i, batch in enumerate(batches, 1):
            print(f"   Batch {i}: {len(batch)} topics")

        return batches
    
    def categorize_batch(self, batch, batch_num, total_batches):
        """Categorize a single batch of topics"""
        print(f"\n🤖 PROCESSING BATCH {batch_num}/{total_batches}")
        print(f"📊 Categorizing {len(batch)} topics")
        print("=" * 50)
        
        # Create topics text for this batch
        topics_text = "\n".join([f"{i+1}. {topic['trending_topic']}" for i, topic in enumerate(batch)])
        
        prompt = f"""You are an expert at categorizing trending topics. Please categorize ALL {len(batch)} trending topics below into the most appropriate category.

CATEGORIES (choose ONE for each topic):
{', '.join(self.categories)}

TRENDING TOPICS TO CATEGORIZE (Batch {batch_num}/{total_batches}):
{topics_text}

INSTRUCTIONS:
1. Categorize each topic into the MOST appropriate category
2. Use ONLY the categories listed above
3. If unsure, use "Other"
4. Return a JSON array with this EXACT format:
[
  {{"topic": "exact topic name", "category": "Category Name"}},
  {{"topic": "exact topic name", "category": "Category Name"}}
]

IMPORTANT: 
- Return ONLY the JSON array, no other text
- Use the EXACT topic names as provided
- Categorize ALL {len(batch)} topics
- Be intelligent about categorization"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000  # Reasonable size for batch
        }
        
        try:
            print(f"🚀 Making API call for batch {batch_num}...")
            
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            ai_response = result['choices'][0]['message']['content'].strip()
            
            print(f"✅ AI response received for batch {batch_num}")
            print(f"📊 Response length: {len(ai_response)} characters")
            
            # Parse the JSON response
            try:
                # Clean the response
                if ai_response.startswith('```json'):
                    ai_response = ai_response.replace('```json', '').replace('```', '').strip()
                elif ai_response.startswith('```'):
                    ai_response = ai_response.replace('```', '').strip()
                
                # Extract JSON array
                if '[' in ai_response:
                    start_idx = ai_response.find('[')
                    ai_response = ai_response[start_idx:]
                
                if ai_response.count(']') > 0:
                    end_idx = ai_response.rfind(']') + 1
                    ai_response = ai_response[:end_idx]
                
                categorizations = json.loads(ai_response)
                print(f"✅ Successfully parsed {len(categorizations)} categorizations")
                
                # Show sample results
                print(f"🎯 Sample results from batch {batch_num}:")
                for i, cat in enumerate(categorizations[:5], 1):
                    print(f"   {i}. {cat['topic'][:40]:<40} → {cat['category']}")
                
                if len(categorizations) > 5:
                    print(f"   ... and {len(categorizations) - 5} more")
                
                return categorizations
                
            except json.JSONDecodeError as e:
                print(f"❌ Error parsing batch {batch_num} response: {e}")
                
                # Save debug file for this batch
                debug_file = f"batch_{batch_num}_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                with open(debug_file, 'w') as f:
                    f.write(ai_response)
                print(f"💾 Batch {batch_num} response saved to {debug_file}")
                
                return []
                
        except Exception as e:
            print(f"❌ Error calling API for batch {batch_num}: {e}")
            return []
    
    def categorize_all_batches(self, topics):
        """Process all topics in multiple batches"""
        print(f"\n🤖 MULTI-BATCH AI CATEGORIZATION")
        print("🧠 Using DeepSeek AI with Smart Batching")
        print("⚡ Ensuring Complete Results!")
        print("=" * 60)
        
        # Split into smaller batches (150 topics each)
        batches = self.split_into_batches(topics, batch_size=150)
        
        all_categorizations = []
        successful_batches = 0
        
        for i, batch in enumerate(batches, 1):
            categorizations = self.categorize_batch(batch, i, len(batches))
            
            if categorizations:
                all_categorizations.extend(categorizations)
                successful_batches += 1
            
            # Rate limiting between batches
            if i < len(batches):
                print(f"⏳ Waiting 3 seconds before next batch...")
                time.sleep(3)
        
        print(f"\n📊 MULTI-BATCH PROCESSING COMPLETE!")
        print(f"✅ Successful batches: {successful_batches}/{len(batches)}")
        print(f"📊 Total categorizations: {len(all_categorizations)}")
        
        return all_categorizations
    
    def merge_and_analyze_results(self, topics, ai_categorizations):
        """Merge AI results with original data and analyze"""
        print(f"\n🔄 MERGING MULTI-BATCH RESULTS")
        print("=" * 50)
        
        # Create lookup dictionary
        ai_lookup = {cat['topic'].lower(): cat['category'] for cat in ai_categorizations}
        
        # Add categories to original topics
        categorized_topics = []
        matched_count = 0
        
        for topic in topics:
            topic_lower = topic['trending_topic'].lower()
            
            if topic_lower in ai_lookup:
                topic['category'] = ai_lookup[topic_lower]
                topic['categorization_method'] = 'AI'
                matched_count += 1
            else:
                topic['category'] = 'Other'
                topic['categorization_method'] = 'Default'
            
            categorized_topics.append(topic)
        
        print(f"✅ Matched {matched_count}/{len(topics)} topics with AI results")
        coverage = (matched_count / len(topics)) * 100
        print(f"📊 AI Coverage: {coverage:.1f}%")
        
        # Generate category summary
        category_counts = {}
        category_topics = {}
        
        for topic in categorized_topics:
            category = topic['category']
            category_counts[category] = category_counts.get(category, 0) + 1
            
            if category not in category_topics:
                category_topics[category] = []
            category_topics[category].append(topic)
        
        # Display summary
        print(f"\n📊 MULTI-BATCH AI CATEGORIZATION SUMMARY")
        print("=" * 70)
        print(f"{'Category':<25} | {'Count':<6} | {'Percentage':<10}")
        print("-" * 70)
        
        total_topics = len(categorized_topics)
        for category in sorted(category_counts.keys(), key=lambda x: category_counts[x], reverse=True):
            count = category_counts[category]
            percentage = (count / total_topics) * 100
            print(f"{category:<25} | {count:<6} | {percentage:<9.1f}%")
        
        print("-" * 70)
        print(f"{'TOTAL':<25} | {total_topics:<6} | {'100.0%':<10}")
        
        # Show Technology topics in detail
        if 'Technology' in category_topics:
            tech_topics = category_topics['Technology']
            print(f"\n🎯 TECHNOLOGY TOPICS FOUND BY MULTI-BATCH AI ({len(tech_topics)} topics):")
            print("=" * 80)
            
            for i, topic in enumerate(tech_topics, 1):
                method = "🤖" if topic['categorization_method'] == 'AI' else "🔄"
                print(f"{i:2d}. {method} {topic['trending_topic']:<45} | {topic['search_volume']}")
        
        return categorized_topics, category_counts, category_topics
    
    def save_results(self, categorized_topics, category_topics):
        """Save all results to CSV files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        print(f"\n💾 SAVING MULTI-BATCH AI RESULTS")
        print("=" * 50)
        
        files_created = []
        
        # Save complete results
        complete_df = pd.DataFrame(categorized_topics)
        complete_file = f"ai_multibatch_all_{timestamp}.csv"
        complete_df.to_csv(complete_file, index=False)
        files_created.append(complete_file)
        print(f"📁 Complete results: {complete_file}")
        
        # Save individual category files
        for category, topics in category_topics.items():
            if len(topics) > 0:
                category_df = pd.DataFrame(topics)
                category_file = f"ai_multibatch_{category.lower().replace(' ', '_')}_{timestamp}.csv"
                category_df.to_csv(category_file, index=False)
                files_created.append(category_file)
                print(f"📁 {category}: {category_file} ({len(topics)} topics)")
        
        return files_created

def main():
    """Main function for multi-batch AI categorization"""
    print("🤖 MULTI-BATCH AI CATEGORIZER")
    print("🧠 DeepSeek AI: Smart Batching for Complete Results")
    print("📊 Processing Your Real Google Trends Data")
    print("⚡ No More Truncated Responses!")
    print("=" * 70)
    
    # Configuration
    api_key = "sk-d137c2aa1d714ef3a4839a3e5b09aded"
    csv_file = "trending_GB_7d_20250606-1129.csv"
    
    # Initialize categorizer
    categorizer = MultiBatchAICategorizer(api_key, csv_file)
    
    # Load data
    topics = categorizer.load_csv_data()
    if not topics:
        print("❌ No data loaded. Exiting.")
        return
    
    # Multi-batch AI categorization
    ai_categorizations = categorizer.categorize_all_batches(topics)
    if not ai_categorizations:
        print("❌ AI categorization failed. Exiting.")
        return
    
    # Merge and analyze results
    categorized_topics, category_counts, category_topics = categorizer.merge_and_analyze_results(topics, ai_categorizations)
    
    # Save results
    files_created = categorizer.save_results(categorized_topics, category_topics)
    
    # Final summary
    tech_count = category_counts.get('Technology', 0)
    ai_coverage = (len(ai_categorizations) / len(topics)) * 100
    
    print(f"\n🎉 MULTI-BATCH AI CATEGORIZATION COMPLETE!")
    print(f"📊 Total topics processed: {len(categorized_topics)}")
    print(f"🎯 Technology topics found: {tech_count}")
    print(f"📈 AI Coverage: {ai_coverage:.1f}%")
    print(f"📁 Files created: {len(files_created)}")
    print(f"🤖 Method: DeepSeek AI (Multi-Batch)")
    print(f"⚡ Complete results with no truncation!")
    
    return categorized_topics

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n✅ SUCCESS! Multi-batch AI categorized {len(results)} topics completely!")
        else:
            print(f"\n❌ FAILED! Multi-batch AI categorization did not complete.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
