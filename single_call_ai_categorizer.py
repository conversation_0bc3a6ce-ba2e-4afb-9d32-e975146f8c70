import pandas as pd
import json
import requests
from datetime import datetime

class SingleCallAICategorizer:
    def __init__(self, api_key, csv_file):
        self.api_key = api_key
        self.csv_file = csv_file
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        
        # Categories from your screenshots
        self.categories = [
            "Technology", "Games", "Sports", "Entertainment", "Business and Finance",
            "Politics", "Health", "Science", "Autos and Vehicles", "Beauty and Fashion",
            "Food and Drink", "Travel and Transportation", "Shopping", "Jobs and Education",
            "Law and Government", "Hobbies and Leisure", "Pets and Animals", "Climate", "Other"
        ]
    
    def load_csv_data(self):
        """Load all trending topics from CSV"""
        print(f"📂 Loading CSV data from: {self.csv_file}")
        
        try:
            df = pd.read_csv(self.csv_file)
            print(f"✅ Loaded {len(df)} rows")
            
            trending_topics = []
            for idx, row in df.iterrows():
                trend = str(row['Trends']).strip()
                volume = str(row['Search volume']).strip()
                
                if trend and trend != 'nan' and len(trend) > 1:
                    trending_topics.append({
                        'trending_topic': trend,
                        'search_volume': volume,
                        'original_index': idx
                    })
            
            print(f"✅ Processed {len(trending_topics)} valid trending topics")
            return trending_topics
            
        except Exception as e:
            print(f"❌ Error loading CSV: {e}")
            return []
    
    def categorize_all_topics_single_call(self, topics):
        """Send ALL topics to DeepSeek AI in ONE request"""
        print(f"\n🤖 SINGLE AI CALL FOR ALL {len(topics)} TOPICS")
        print("🧠 Using DeepSeek AI for Complete Categorization")
        print("=" * 60)
        
        # Create the complete topics list
        topics_text = "\n".join([f"{i+1}. {topic['trending_topic']}" for i, topic in enumerate(topics)])
        
        prompt = f"""You are an expert at categorizing trending topics. Please categorize ALL {len(topics)} trending topics below into the most appropriate category.

CATEGORIES (choose ONE for each topic):
{', '.join(self.categories)}

TRENDING TOPICS TO CATEGORIZE:
{topics_text}

INSTRUCTIONS:
1. Categorize each topic into the MOST appropriate category
2. Use ONLY the categories listed above
3. If unsure, use "Other"
4. Return a JSON array with this EXACT format:
[
  {{"topic": "exact topic name", "category": "Category Name"}},
  {{"topic": "exact topic name", "category": "Category Name"}}
]

IMPORTANT: 
- Return ONLY the JSON array, no other text
- Use the EXACT topic names as provided
- Categorize ALL {len(topics)} topics
- Be intelligent about categorization (e.g., "nintendo switch 2" = Technology, "premier league" = Sports)"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 8000  # Increased for large response
        }
        
        try:
            print(f"🚀 Making SINGLE API call to categorize ALL {len(topics)} topics...")
            print("⏳ This may take a moment...")
            
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            ai_response = result['choices'][0]['message']['content'].strip()
            
            print("✅ AI response received!")
            print(f"📊 Response length: {len(ai_response)} characters")
            
            # Parse the JSON response
            try:
                # Clean the response more thoroughly
                print("🔧 Cleaning AI response...")

                # Remove any text before the JSON array
                if '[' in ai_response:
                    start_idx = ai_response.find('[')
                    ai_response = ai_response[start_idx:]

                # Remove any text after the JSON array
                if ai_response.count(']') > 0:
                    # Find the last closing bracket
                    end_idx = ai_response.rfind(']') + 1
                    ai_response = ai_response[:end_idx]

                # Remove markdown formatting
                ai_response = ai_response.replace('```json', '').replace('```', '').strip()

                print(f"🔧 Cleaned response length: {len(ai_response)} characters")
                print(f"🔧 Response starts with: {ai_response[:100]}...")

                categorizations = json.loads(ai_response)
                print(f"✅ AI successfully categorized {len(categorizations)} topics")
                
                # Show sample results
                print(f"\n🎯 SAMPLE AI CATEGORIZATIONS:")
                print("-" * 50)
                for i, cat in enumerate(categorizations[:10], 1):
                    print(f"{i:2d}. {cat['topic'][:40]:<40} → {cat['category']}")
                
                if len(categorizations) > 10:
                    print(f"... and {len(categorizations) - 10} more topics")
                
                return categorizations
                
            except json.JSONDecodeError as e:
                print(f"❌ Error parsing AI response: {e}")
                print(f"Raw response preview: {ai_response[:500]}...")
                
                # Try to save the raw response for debugging
                with open(f"ai_response_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w') as f:
                    f.write(ai_response)
                print("💾 Raw response saved for debugging")
                
                return []
                
        except Exception as e:
            print(f"❌ Error calling DeepSeek API: {e}")
            return []
    
    def merge_and_analyze_results(self, topics, ai_categorizations):
        """Merge AI results with original data and analyze"""
        print(f"\n🔄 MERGING AI RESULTS WITH ORIGINAL DATA")
        print("=" * 50)
        
        # Create lookup dictionary
        ai_lookup = {cat['topic'].lower(): cat['category'] for cat in ai_categorizations}
        
        # Add categories to original topics
        categorized_topics = []
        matched_count = 0
        
        for topic in topics:
            topic_lower = topic['trending_topic'].lower()
            
            if topic_lower in ai_lookup:
                topic['category'] = ai_lookup[topic_lower]
                topic['categorization_method'] = 'AI'
                matched_count += 1
            else:
                topic['category'] = 'Other'
                topic['categorization_method'] = 'Default'
            
            categorized_topics.append(topic)
        
        print(f"✅ Matched {matched_count}/{len(topics)} topics with AI results")
        
        # Generate category summary
        category_counts = {}
        category_topics = {}
        
        for topic in categorized_topics:
            category = topic['category']
            category_counts[category] = category_counts.get(category, 0) + 1
            
            if category not in category_topics:
                category_topics[category] = []
            category_topics[category].append(topic)
        
        # Display summary
        print(f"\n📊 AI CATEGORIZATION SUMMARY")
        print("=" * 60)
        print(f"{'Category':<25} | {'Count':<6} | {'Percentage':<10}")
        print("-" * 60)
        
        total_topics = len(categorized_topics)
        for category in sorted(category_counts.keys(), key=lambda x: category_counts[x], reverse=True):
            count = category_counts[category]
            percentage = (count / total_topics) * 100
            print(f"{category:<25} | {count:<6} | {percentage:<9.1f}%")
        
        print("-" * 60)
        print(f"{'TOTAL':<25} | {total_topics:<6} | {'100.0%':<10}")
        
        # Show Technology topics in detail
        if 'Technology' in category_topics:
            tech_topics = category_topics['Technology']
            print(f"\n🎯 TECHNOLOGY TOPICS FOUND BY AI ({len(tech_topics)} topics):")
            print("=" * 70)
            
            for i, topic in enumerate(tech_topics, 1):
                print(f"{i:2d}. {topic['trending_topic']:<50} | {topic['search_volume']}")
        
        return categorized_topics, category_counts, category_topics
    
    def save_results(self, categorized_topics, category_topics):
        """Save all results to CSV files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        print(f"\n💾 SAVING AI CATEGORIZATION RESULTS")
        print("=" * 50)
        
        files_created = []
        
        # Save complete results
        complete_df = pd.DataFrame(categorized_topics)
        complete_file = f"ai_single_call_all_{timestamp}.csv"
        complete_df.to_csv(complete_file, index=False)
        files_created.append(complete_file)
        print(f"📁 Complete results: {complete_file}")
        
        # Save individual category files
        for category, topics in category_topics.items():
            if len(topics) > 0:
                category_df = pd.DataFrame(topics)
                category_file = f"ai_single_{category.lower().replace(' ', '_')}_{timestamp}.csv"
                category_df.to_csv(category_file, index=False)
                files_created.append(category_file)
                print(f"📁 {category}: {category_file} ({len(topics)} topics)")
        
        # Save summary
        summary_data = []
        for category, topics in category_topics.items():
            summary_data.append({
                'category': category,
                'count': len(topics),
                'percentage': (len(topics) / len(categorized_topics)) * 100
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_file = f"ai_single_summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        files_created.append(summary_file)
        print(f"📁 Summary: {summary_file}")
        
        return files_created

def main():
    """Main function for single-call AI categorization"""
    print("🤖 SINGLE-CALL AI CATEGORIZER")
    print("🧠 DeepSeek AI: ONE Call for ALL Topics")
    print("📊 Processing Your Real Google Trends Data")
    print("⚡ Much More Efficient Than Multiple Calls!")
    print("=" * 70)
    
    # Configuration
    api_key = "sk-d137c2aa1d714ef3a4839a3e5b09aded"
    csv_file = "trending_GB_7d_20250606-1129.csv"
    
    # Initialize categorizer
    categorizer = SingleCallAICategorizer(api_key, csv_file)
    
    # Load data
    topics = categorizer.load_csv_data()
    if not topics:
        print("❌ No data loaded. Exiting.")
        return
    
    # Single AI call for all topics
    ai_categorizations = categorizer.categorize_all_topics_single_call(topics)
    if not ai_categorizations:
        print("❌ AI categorization failed. Exiting.")
        return
    
    # Merge and analyze results
    categorized_topics, category_counts, category_topics = categorizer.merge_and_analyze_results(topics, ai_categorizations)
    
    # Save results
    files_created = categorizer.save_results(categorized_topics, category_topics)
    
    # Final summary
    tech_count = category_counts.get('Technology', 0)
    print(f"\n🎉 SINGLE-CALL AI CATEGORIZATION COMPLETE!")
    print(f"📊 Total topics processed: {len(categorized_topics)}")
    print(f"🎯 Technology topics found: {tech_count}")
    print(f"📁 Files created: {len(files_created)}")
    print(f"🤖 Method: DeepSeek AI (Single Call)")
    print(f"⚡ Much more efficient than keyword matching!")
    
    return categorized_topics

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n✅ SUCCESS! AI intelligently categorized {len(results)} topics in ONE call!")
        else:
            print(f"\n❌ FAILED! AI categorization did not complete.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
